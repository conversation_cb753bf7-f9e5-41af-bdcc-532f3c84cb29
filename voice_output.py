#!/usr/bin/env python3
"""
Voice Output Module for TeleConnect Customer Support RAG Bot
Provides text-to-speech functionality for bot responses
"""

import streamlit as st
import pyttsx3
import tempfile
import os
import threading
import time
from typing import Optional, Dict, Any
import logging
import io
import base64
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class VoiceOutputHandler:
    """
    Handles text-to-speech functionality for bot responses
    """
    
    def __init__(self):
        """Initialize voice output handler"""
        self.tts_engine = None
        self.is_speaking = False
        self.audio_file_path = None
        
        # Voice settings
        self.voice_rate = 180  # Words per minute
        self.voice_volume = 0.9  # Volume level (0.0 to 1.0)
        self.voice_id = None  # Will be set based on available voices
        
        # Initialize TTS engine
        self._initialize_tts_engine()
    
    def _initialize_tts_engine(self):
        """Initialize text-to-speech engine with error handling"""
        try:
            self.tts_engine = pyttsx3.init()
            
            # Configure voice settings
            self.tts_engine.setProperty('rate', self.voice_rate)
            self.tts_engine.setProperty('volume', self.voice_volume)
            
            # Get available voices and select a good one
            voices = self.tts_engine.getProperty('voices')
            if voices:
                # Prefer female voice for customer service
                for voice in voices:
                    if 'female' in voice.name.lower() or 'zira' in voice.name.lower():
                        self.voice_id = voice.id
                        break
                
                # If no female voice found, use first available
                if not self.voice_id and voices:
                    self.voice_id = voices[0].id
                
                if self.voice_id:
                    self.tts_engine.setProperty('voice', self.voice_id)
            
            logger.info("TTS engine initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize TTS engine: {e}")
            self.tts_engine = None
    
    def is_tts_available(self) -> bool:
        """Check if text-to-speech is available"""
        return self.tts_engine is not None
    
    def get_available_voices(self) -> list:
        """Get list of available voices"""
        if not self.tts_engine:
            return []
        
        try:
            voices = self.tts_engine.getProperty('voices')
            return [{"id": voice.id, "name": voice.name} for voice in voices] if voices else []
        except Exception as e:
            logger.error(f"Error getting voices: {e}")
            return []
    
    def set_voice_settings(self, rate: int = None, volume: float = None, voice_id: str = None):
        """
        Update voice settings
        
        Args:
            rate: Speech rate (words per minute)
            volume: Volume level (0.0 to 1.0)
            voice_id: Voice ID to use
        """
        if not self.tts_engine:
            return
        
        try:
            if rate is not None:
                self.voice_rate = rate
                self.tts_engine.setProperty('rate', rate)
            
            if volume is not None:
                self.voice_volume = volume
                self.tts_engine.setProperty('volume', volume)
            
            if voice_id is not None:
                self.voice_id = voice_id
                self.tts_engine.setProperty('voice', voice_id)
                
        except Exception as e:
            logger.error(f"Error setting voice properties: {e}")
    
    def speak_text(self, text: str) -> bool:
        """
        Convert text to speech and play it
        
        Args:
            text: Text to convert to speech
            
        Returns:
            True if successful, False otherwise
        """
        if not self.tts_engine or not text.strip():
            return False
        
        try:
            # Clean text for better speech
            clean_text = self._clean_text_for_speech(text)
            
            # Speak the text
            self.is_speaking = True
            self.tts_engine.say(clean_text)
            self.tts_engine.runAndWait()
            self.is_speaking = False
            
            return True
            
        except Exception as e:
            logger.error(f"Error in text-to-speech: {e}")
            self.is_speaking = False
            return False
    
    def speak_text_async(self, text: str) -> threading.Thread:
        """
        Convert text to speech asynchronously
        
        Args:
            text: Text to convert to speech
            
        Returns:
            Thread object
        """
        def speak_worker():
            self.speak_text(text)
        
        thread = threading.Thread(target=speak_worker, daemon=True)
        thread.start()
        return thread
    
    def generate_audio_file(self, text: str, filename: str = None) -> Optional[str]:
        """
        Generate audio file from text
        
        Args:
            text: Text to convert to speech
            filename: Output filename (optional)
            
        Returns:
            Path to generated audio file or None
        """
        if not self.tts_engine or not text.strip():
            return None
        
        try:
            # Create temporary file if no filename provided
            if not filename:
                temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=".wav")
                filename = temp_file.name
                temp_file.close()
            
            # Clean text for better speech
            clean_text = self._clean_text_for_speech(text)
            
            # Save to file
            self.tts_engine.save_to_file(clean_text, filename)
            self.tts_engine.runAndWait()
            
            return filename
            
        except Exception as e:
            logger.error(f"Error generating audio file: {e}")
            return None
    
    def _clean_text_for_speech(self, text: str) -> str:
        """
        Clean text for better text-to-speech output
        
        Args:
            text: Raw text
            
        Returns:
            Cleaned text optimized for speech
        """
        # Remove markdown formatting
        clean_text = text.replace("**", "").replace("*", "")
        clean_text = clean_text.replace("###", "").replace("##", "").replace("#", "")
        
        # Replace common abbreviations with full words
        replacements = {
            "TeleConnect": "Tele Connect",
            "WiFi": "Wi-Fi",
            "1-800-TELECON": "1 800 TELECON",
            "1-800-TECH-HELP": "1 800 TECH HELP",
            "1-800-BILLING": "1 800 BILLING",
            "Mbps": "megabits per second",
            "GB": "gigabytes",
            "TB": "terabytes",
            "24/7": "24 hours a day, 7 days a week",
            "FAQ": "frequently asked questions",
            "URL": "web address",
            "IP": "I P",
            "DNS": "D N S",
            "ISP": "internet service provider"
        }
        
        for abbrev, full_form in replacements.items():
            clean_text = clean_text.replace(abbrev, full_form)
        
        # Remove excessive punctuation
        clean_text = clean_text.replace("...", ". ")
        clean_text = clean_text.replace("!!", "!")
        clean_text = clean_text.replace("??", "?")
        
        # Add pauses for better speech flow
        clean_text = clean_text.replace(". ", ". ... ")
        clean_text = clean_text.replace("! ", "! ... ")
        clean_text = clean_text.replace("? ", "? ... ")
        
        return clean_text.strip()
    
    def stop_speaking(self):
        """Stop current speech output"""
        if self.tts_engine and self.is_speaking:
            try:
                self.tts_engine.stop()
                self.is_speaking = False
            except Exception as e:
                logger.error(f"Error stopping speech: {e}")

def create_voice_output_controls():
    """
    Create voice output controls for Streamlit
    
    Returns:
        Dictionary with voice output settings
    """
    
    # Initialize voice handler in session state
    if 'voice_output_handler' not in st.session_state:
        st.session_state.voice_output_handler = VoiceOutputHandler()
    
    voice_handler = st.session_state.voice_output_handler
    
    # Voice output section
    st.markdown("### 🔊 Voice Output Settings")
    
    if not voice_handler.is_tts_available():
        st.error("🚫 Text-to-speech not available. Please check your system audio settings.")
        return {"enabled": False}
    
    # Voice output controls
    col1, col2 = st.columns(2)
    
    with col1:
        # Enable/disable voice output
        voice_enabled = st.checkbox("🔊 Enable Voice Responses", 
                                   value=st.session_state.get('voice_output_enabled', False),
                                   help="Bot will speak responses aloud")
        st.session_state.voice_output_enabled = voice_enabled
    
    with col2:
        # Auto-play setting
        auto_play = st.checkbox("▶️ Auto-play Responses", 
                               value=st.session_state.get('voice_auto_play', True),
                               help="Automatically play voice when response is generated")
        st.session_state.voice_auto_play = auto_play
    
    # Voice settings
    if voice_enabled:
        with st.expander("🎛️ Voice Settings"):
            # Speech rate
            rate = st.slider("Speech Rate (words/minute)", 100, 300, 180)
            
            # Volume
            volume = st.slider("Volume", 0.0, 1.0, 0.9, 0.1)
            
            # Voice selection
            voices = voice_handler.get_available_voices()
            if voices:
                voice_names = [f"{voice['name']}" for voice in voices]
                selected_voice = st.selectbox("Voice", voice_names)
                
                # Find selected voice ID
                voice_id = None
                for voice in voices:
                    if voice['name'] == selected_voice:
                        voice_id = voice['id']
                        break
            else:
                voice_id = None
            
            # Apply settings
            if st.button("Apply Voice Settings"):
                voice_handler.set_voice_settings(rate=rate, volume=volume, voice_id=voice_id)
                st.success("Voice settings updated!")
    
    return {
        "enabled": voice_enabled,
        "auto_play": auto_play,
        "handler": voice_handler
    }

def create_voice_output_button(text: str, button_text: str = "🔊 Listen") -> bool:
    """
    Create a button to play text as speech
    
    Args:
        text: Text to convert to speech
        button_text: Button label
        
    Returns:
        True if button was clicked and speech played
    """
    
    if 'voice_output_handler' not in st.session_state:
        st.session_state.voice_output_handler = VoiceOutputHandler()
    
    voice_handler = st.session_state.voice_output_handler
    
    if not voice_handler.is_tts_available():
        return False
    
    if st.button(button_text, help="Click to hear this response"):
        with st.spinner("🔊 Speaking..."):
            success = voice_handler.speak_text(text)
            if success:
                st.success("✅ Voice output played")
                return True
            else:
                st.error("❌ Voice output failed")
    
    return False

def play_response_voice(text: str, auto_play: bool = False) -> bool:
    """
    Play response as voice output
    
    Args:
        text: Response text to speak
        auto_play: Whether to play automatically
        
    Returns:
        True if voice was played successfully
    """
    
    if 'voice_output_handler' not in st.session_state:
        st.session_state.voice_output_handler = VoiceOutputHandler()
    
    voice_handler = st.session_state.voice_output_handler
    
    if not voice_handler.is_tts_available():
        return False
    
    # Check if voice output is enabled
    voice_enabled = st.session_state.get('voice_output_enabled', False)
    if not voice_enabled:
        return False
    
    try:
        if auto_play and st.session_state.get('voice_auto_play', True):
            # Play automatically in background
            thread = voice_handler.speak_text_async(text)
            return True
        else:
            # Manual play
            return voice_handler.speak_text(text)
            
    except Exception as e:
        logger.error(f"Error playing voice response: {e}")
        return False

def create_audio_player_html(audio_file_path: str) -> str:
    """
    Create HTML audio player for generated speech
    
    Args:
        audio_file_path: Path to audio file
        
    Returns:
        HTML string for audio player
    """
    
    try:
        # Read audio file and encode as base64
        with open(audio_file_path, "rb") as audio_file:
            audio_bytes = audio_file.read()
            audio_base64 = base64.b64encode(audio_bytes).decode()
        
        # Create HTML audio player
        audio_html = f"""
        <audio controls style="width: 100%;">
            <source src="data:audio/wav;base64,{audio_base64}" type="audio/wav">
            Your browser does not support the audio element.
        </audio>
        """
        
        return audio_html
        
    except Exception as e:
        logger.error(f"Error creating audio player: {e}")
        return "<p>Audio player not available</p>"

# Test function
def test_voice_output():
    """Test voice output functionality"""
    
    print("=== Testing Voice Output ===")
    
    voice_handler = VoiceOutputHandler()
    
    if not voice_handler.is_tts_available():
        print("❌ TTS not available")
        return False
    
    print("✅ TTS engine initialized")
    
    # Test voices
    voices = voice_handler.get_available_voices()
    print(f"✅ Available voices: {len(voices)}")
    
    # Test speech
    test_text = "Hello! This is a test of the TeleConnect voice output system."
    print("🔊 Testing speech...")
    
    success = voice_handler.speak_text(test_text)
    if success:
        print("✅ Speech test successful")
        return True
    else:
        print("❌ Speech test failed")
        return False

# Example usage
if __name__ == "__main__":
    test_voice_output()
