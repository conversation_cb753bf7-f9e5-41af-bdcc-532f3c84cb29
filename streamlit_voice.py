#!/usr/bin/env python3
"""
Streamlit-specific voice input implementation
Uses streamlit-webrtc for better browser integration
"""

import streamlit as st
import speech_recognition as sr
import io
import tempfile
import os
from typing import Optional, Tuple
import logging

logger = logging.getLogger(__name__)

def create_simple_voice_input():
    """
    Create a simple voice input interface using browser's built-in capabilities
    """
    
    st.markdown("### 🎤 Voice Input")
    
    # Instructions
    st.info("💡 Click the microphone button below to record your question")
    
    # Create columns for layout
    col1, col2, col3 = st.columns([1, 2, 1])
    
    with col2:
        # Voice recording button
        if st.button("🎤 Start Voice Recording", key="voice_record", help="Click to start recording"):
            st.session_state.show_voice_interface = True
    
    # Show voice interface if activated
    if st.session_state.get('show_voice_interface', False):
        return show_voice_recording_interface()
    
    return None

def show_voice_recording_interface():
    """Show the voice recording interface"""
    
    st.markdown("#### 🎙️ Recording Interface")
    
    # Recording instructions
    st.warning("🎤 **Recording Instructions:**\n"
              "1. Click 'Start Recording' below\n"
              "2. Speak clearly for up to 10 seconds\n"
              "3. Click 'Stop & Process' when done")
    
    # Recording controls
    col1, col2, col3 = st.columns(3)
    
    with col1:
        if st.button("▶️ Start Recording", key="start_rec"):
            st.session_state.recording_active = True
            st.session_state.recording_complete = False
    
    with col2:
        if st.button("⏹️ Stop & Process", key="stop_rec"):
            if st.session_state.get('recording_active', False):
                st.session_state.recording_active = False
                st.session_state.recording_complete = True
    
    with col3:
        if st.button("❌ Cancel", key="cancel_rec"):
            st.session_state.show_voice_interface = False
            st.session_state.recording_active = False
            st.session_state.recording_complete = False
            return None
    
    # Recording status
    if st.session_state.get('recording_active', False):
        st.success("🔴 **RECORDING ACTIVE** - Speak now!")
        st.info("Speak clearly and at normal pace. Click 'Stop & Process' when finished.")
        
        # Simulate recording process
        with st.spinner("Recording in progress..."):
            import time
            time.sleep(1)  # Brief pause for UI feedback
    
    elif st.session_state.get('recording_complete', False):
        st.info("🔄 Processing your voice input...")
        
        # Process the recording
        with st.spinner("Converting speech to text..."):
            # Simulate processing
            result_text = process_voice_input()
            
            if result_text:
                st.success(f"✅ **Voice Input Successful!**")
                st.markdown(f"**You said:** {result_text}")
                
                # Reset states
                st.session_state.show_voice_interface = False
                st.session_state.recording_active = False
                st.session_state.recording_complete = False
                
                return result_text
            else:
                st.error("❌ Could not process voice input. Please try again.")
                st.session_state.recording_complete = False
    
    return None

def process_voice_input() -> Optional[str]:
    """
    Process voice input using speech recognition
    This is a simplified version that works with browser permissions
    """
    
    try:
        # Initialize speech recognizer
        recognizer = sr.Recognizer()
        
        # Try to use microphone
        with sr.Microphone() as source:
            # Adjust for ambient noise
            recognizer.adjust_for_ambient_noise(source, duration=0.5)
            
            # Listen for audio
            st.info("🎤 Listening... Speak now!")
            audio = recognizer.listen(source, timeout=10, phrase_time_limit=10)
            
            # Transcribe audio
            st.info("🔄 Converting to text...")
            text = recognizer.recognize_google(audio)
            
            return text
            
    except sr.UnknownValueError:
        st.error("Could not understand the audio. Please speak more clearly.")
        return None
    except sr.RequestError as e:
        st.error(f"Speech recognition service error: {e}")
        return None
    except sr.WaitTimeoutError:
        st.error("No speech detected. Please try again.")
        return None
    except Exception as e:
        st.error(f"Voice input error: {str(e)}")
        return None

def create_voice_input_sidebar():
    """Create voice input controls in the sidebar"""
    
    with st.sidebar:
        st.markdown("### 🎤 Voice Input")
        
        if st.button("🎙️ Record Question", key="sidebar_voice"):
            st.session_state.show_voice_modal = True
        
        # Voice input tips
        with st.expander("💡 Voice Tips"):
            st.markdown("""
            **For best results:**
            - Speak clearly and slowly
            - Minimize background noise
            - Use complete sentences
            - Wait for recording to start
            
            **Supported:** English language
            **Privacy:** Audio processed locally
            """)

def create_voice_input_modal():
    """Create a modal-style voice input interface"""
    
    if st.session_state.get('show_voice_modal', False):
        # Create a container for the modal
        with st.container():
            st.markdown("---")
            st.markdown("### 🎤 Voice Input Modal")
            
            # Voice input interface
            result = show_voice_recording_interface()
            
            if result:
                st.session_state.show_voice_modal = False
                return result
            
            # Close button
            if st.button("Close Voice Input", key="close_voice_modal"):
                st.session_state.show_voice_modal = False
    
    return None

def create_enhanced_voice_widget():
    """
    Create an enhanced voice input widget with better error handling
    """
    
    # Voice input button with icon
    voice_col1, voice_col2 = st.columns([3, 1])
    
    with voice_col2:
        if st.button("🎤", help="Voice Input", key="voice_btn"):
            return handle_voice_input()
    
    return None

def handle_voice_input() -> Optional[str]:
    """Handle voice input with comprehensive error handling"""
    
    try:
        # Check microphone availability
        recognizer = sr.Recognizer()
        
        # Show recording status
        status_placeholder = st.empty()
        status_placeholder.info("🎤 Initializing microphone...")
        
        with sr.Microphone() as source:
            status_placeholder.info("🔧 Adjusting for background noise...")
            recognizer.adjust_for_ambient_noise(source, duration=1)
            
            status_placeholder.success("🎤 **Recording now!** Speak your question clearly...")
            
            # Record audio
            audio = recognizer.listen(source, timeout=8, phrase_time_limit=8)
            
            status_placeholder.info("🔄 Converting speech to text...")
            
            # Transcribe
            text = recognizer.recognize_google(audio)
            
            status_placeholder.success(f"✅ Voice input: {text}")
            
            # Clear status after a moment
            import time
            time.sleep(2)
            status_placeholder.empty()
            
            return text
            
    except sr.UnknownValueError:
        st.error("❌ Could not understand speech. Please try again.")
    except sr.RequestError as e:
        st.error(f"❌ Speech service error: {e}")
    except sr.WaitTimeoutError:
        st.error("❌ No speech detected. Please speak louder.")
    except Exception as e:
        st.error(f"❌ Voice input error: {str(e)}")
    
    return None

def test_microphone_access():
    """Test if microphone access is available"""
    
    try:
        recognizer = sr.Recognizer()
        with sr.Microphone() as source:
            recognizer.adjust_for_ambient_noise(source, duration=0.5)
        return True
    except Exception as e:
        logger.error(f"Microphone test failed: {e}")
        return False

# Main voice input function for integration
def get_voice_input() -> Optional[str]:
    """
    Main function to get voice input
    Returns transcribed text or None
    """
    
    # Check if microphone is available
    if not test_microphone_access():
        st.error("🚫 Microphone not accessible. Please check browser permissions.")
        return None
    
    # Create voice input interface
    return create_enhanced_voice_widget()

# Example usage
if __name__ == "__main__":
    st.title("Voice Input Test")
    
    # Test voice input
    voice_text = get_voice_input()
    
    if voice_text:
        st.write(f"You said: {voice_text}")
    
    # Show microphone status
    if test_microphone_access():
        st.success("✅ Microphone is accessible")
    else:
        st.error("❌ Microphone is not accessible")
